import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import networkx as nx
from matplotlib.patches import FancyBboxPatch
import numpy as np

def create_labeled_topology():
    """创建带完整标签的网络拓扑图"""
    
    print("读取文件: cnh_20250805_162949.xlsx")
    
    # 读取数据
    df = pd.read_excel('cnh_20250805_162949.xlsx')
    
    # 收集所有网元和连接关系
    nodes = {}  # {设备名: 设备级别}
    connections = []  # [(A端, Z端)]
    node_details = {}  # {设备名: 详细信息}
    
    for idx, row in df.iterrows():
        a_device = row['A1']
        a_level = row['A1_LV']
        
        # 添加A端设备
        if pd.notna(a_device):
            nodes[a_device] = a_level
            node_details[a_device] = {
                'level': a_level,
                'subnet': row['关联传输子网'],
                'city': row['地市名称'],
                'county': row['区/县名称']
            }
        
        # 查找所有Z端设备
        for z_num in [1, 2, 3]:  # Z1, Z2, Z3
            z_col = f'Z{z_num}'
            z_lv_col = f'Z{z_num}_LV'
            
            if z_col in df.columns and z_lv_col in df.columns:
                z_device = row[z_col]
                z_level = row[z_lv_col]
                
                if pd.notna(z_device) and pd.notna(z_level):
                    nodes[z_device] = z_level
                    connections.append((a_device, z_device))
                    
                    if z_device not in node_details:
                        node_details[z_device] = {
                            'level': z_level,
                            'subnet': 'Unknown',
                            'city': 'Unknown',
                            'county': 'Unknown'
                        }
    
    print(f"发现 {len(nodes)} 个网元，{len(connections)} 个连接")
    
    # 创建网络图
    G = nx.Graph()
    
    # 添加节点
    for device, level in nodes.items():
        G.add_node(device, level=level)
    
    # 添加边
    for a, z in connections:
        if a in nodes and z in nodes:
            G.add_edge(a, z)
    
    # 使用spring布局算法自动排列节点
    pos = nx.spring_layout(G, k=5, iterations=100, seed=42)
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(20, 16))
    
    # 绘制连接线
    for edge in G.edges():
        x1, y1 = pos[edge[0]]
        x2, y2 = pos[edge[1]]
        ax.plot([x1, x2], [y1, y2], 'gray', linewidth=2, alpha=0.6, zorder=1)
    
    # 绘制节点和标签
    for device, (x, y) in pos.items():
        level = nodes[device]
        
        # 根据设备级别选择颜色
        if level == '本地接入':
            color = '#e1f5fe'
            edge_color = '#01579b'
        elif level == '本地汇聚':
            color = '#e8f5e8'
            edge_color = '#2e7d32'
        else:
            color = 'lightgray'
            edge_color = 'black'
        
        # 提取设备关键信息
        device_info = extract_device_info(device)
        
        # 绘制设备框
        width = 0.25
        height = 0.12
        
        rect = FancyBboxPatch(
            (x - width/2, y - height/2), width, height,
            boxstyle="round,pad=0.02",
            facecolor=color,
            edgecolor=edge_color,
            linewidth=2,
            zorder=2
        )
        ax.add_patch(rect)
        
        # 添加设备信息文本
        ax.text(x, y, device_info['display'], 
               ha='center', va='center', 
               fontsize=8, weight='bold',
               zorder=3)
        
        # 在节点旁边添加完整设备名称
        ax.text(x, y - height/2 - 0.05, device_info['full_name'], 
               ha='center', va='top', 
               fontsize=6, style='italic',
               zorder=3, 
               bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))
    
    # 添加图例
    legend_elements = [
        patches.Rectangle((0, 0), 1, 1, facecolor='#e1f5fe', edgecolor='#01579b', label='本地接入 (10个)'),
        patches.Rectangle((0, 0), 1, 1, facecolor='#e8f5e8', edgecolor='#2e7d32', label='本地汇聚 (2个)')
    ]
    ax.legend(handles=legend_elements, loc='upper right', fontsize=12)
    
    # 设置标题
    ax.set_title('苍南环32网络拓扑图 - 完整标签版本', fontsize=18, weight='bold', pad=30)
    ax.axis('off')
    
    # 添加说明
    ax.text(0.02, 0.98, '图例说明：\n• 节点内：设备ID和关键位置信息\n• 节点下：完整设备名称\n• 蓝色：本地接入设备\n• 绿色：本地汇聚设备', 
            transform=ax.transAxes, fontsize=10, verticalalignment='top',
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('labeled_network_topology.png', dpi=300, bbox_inches='tight')
    print("带标签的网络拓扑图已保存为: labeled_network_topology.png")
    
    # 显示图表
    plt.show()
    
    # 打印设备信息
    print(f"\n=== 设备详细信息 ===")
    for device, level in nodes.items():
        device_info = extract_device_info(device)
        print(f"{device_info['id']}: {device} ({level})")
    
    return G, nodes, connections

def extract_device_info(device):
    """提取设备关键信息"""
    if '-' in device:
        parts = device.split('-')
        if len(parts) >= 4:
            # 格式: 176-29148-温州苍南甬台温福高铁仙堂隧道南口-PTN970C
            device_id = parts[1]  # 29148
            location = parts[2]   # 温州苍南甬台温福高铁仙堂隧道南口
            device_type = parts[3] # PTN970C
            
            # 简化地点名称
            location_short = location
            if '温州' in location:
                location_short = location.replace('温州苍南甬台温福高铁', '高铁-')
                location_short = location_short.replace('温州苍南', '')
                location_short = location_short.replace('温州市苍南', '')
                location_short = location_short.replace('苍南桥墩', '桥墩-')
                location_short = location_short.replace('苍南', '')
            
            # 进一步简化
            if len(location_short) > 15:
                location_short = location_short[:12] + '...'
            
            return {
                'id': device_id,
                'location': location_short,
                'type': device_type,
                'display': f"{device_id}\n{location_short}\n{device_type}",
                'full_name': device
            }
        else:
            return {
                'id': parts[-1],
                'location': '',
                'type': '',
                'display': parts[-1],
                'full_name': device
            }
    else:
        return {
            'id': device,
            'location': '',
            'type': '',
            'display': device,
            'full_name': device
        }

if __name__ == "__main__":
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    create_labeled_topology()
