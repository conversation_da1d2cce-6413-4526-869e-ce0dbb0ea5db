import pandas as pd

def verify_result():
    """验证处理结果"""
    
    # 读取原始文件和结果文件
    df_original = pd.read_excel('苍南环32.xlsx')
    df_result = pd.read_excel('苍南环32_补充完成.xlsx')
    
    print("=== 验证结果 ===")
    print(f"原始文件行数: {len(df_original)}")
    print(f"结果文件行数: {len(df_result)}")
    print(f"原始文件列数: {len(df_original.columns)}")
    print(f"结果文件列数: {len(df_result.columns)}")
    
    print("\n=== Q列以后的数据补充情况 ===")
    
    # 检查Q列以后的数据（从第16列开始，即Q列）
    for idx in range(len(df_result)):
        row_name = df_result.iloc[idx, 0]  # A列的设备名称
        print(f"\n第{idx+1}行 ({row_name}):")
        
        # 检查从Q列开始的数据
        col_start = 16  # Q列
        group_num = 1
        
        for col in range(col_start, len(df_result.columns), 3):
            if col + 2 < len(df_result.columns):
                gl_val = df_result.iloc[idx, col]
                remote_val = df_result.iloc[idx, col + 1]
                remote_lv_val = df_result.iloc[idx, col + 2]
                
                if pd.notna(gl_val) or pd.notna(remote_val) or pd.notna(remote_lv_val):
                    print(f"  第{group_num}组: gl{group_num}={gl_val}")
                    print(f"           remote{group_num}={remote_val}")
                    print(f"           remote{group_num}_lv={remote_lv_val}")
                    group_num += 1
                else:
                    break
    
    print("\n=== 数据统计 ===")
    # 统计每行补充了多少组数据
    for idx in range(len(df_result)):
        row_name = df_result.iloc[idx, 0]
        groups_count = 0
        
        col_start = 16  # Q列
        for col in range(col_start, len(df_result.columns), 3):
            if col + 2 < len(df_result.columns):
                gl_val = df_result.iloc[idx, col]
                if pd.notna(gl_val):
                    groups_count += 1
                else:
                    break
        
        print(f"第{idx+1}行: 补充了{groups_count}组数据")

if __name__ == "__main__":
    verify_result()
