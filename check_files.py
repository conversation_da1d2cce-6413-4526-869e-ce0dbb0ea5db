import pandas as pd

def check_all_files():
    """检查所有文件的结构"""
    
    files = ['苍南环32.xlsx', '苍南环32_补充完成.xlsx']
    
    for filename in files:
        try:
            print(f"\n=== {filename} ===")
            df = pd.read_excel(filename)
            print(f"行数: {len(df)}")
            print(f"列数: {len(df.columns)}")
            
            print("列名:")
            for i, col in enumerate(df.columns):
                print(f"  {chr(65+i)}列: {col}")
            
            print("\n前2行数据:")
            print(df.head(2))
            
        except Exception as e:
            print(f"读取{filename}失败: {e}")

if __name__ == "__main__":
    check_all_files()
