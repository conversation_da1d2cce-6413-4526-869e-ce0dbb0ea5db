import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import networkx as nx
from matplotlib.patches import FancyBboxPatch
import numpy as np

def create_matplotlib_interactive():
    """创建matplotlib交互式网络拓扑图"""
    
    print("读取文件: cnh_20250805_162949.xlsx")
    
    # 读取数据
    df = pd.read_excel('cnh_20250805_162949.xlsx')
    
    # 收集所有网元和连接关系
    nodes = {}  # {设备名: 设备级别}
    connections = []  # [(A端, Z端)]
    node_details = {}  # {设备名: 详细信息}
    
    for idx, row in df.iterrows():
        a_device = row['A1']
        a_level = row['A1_LV']
        
        # 添加A端设备
        if pd.notna(a_device):
            nodes[a_device] = a_level
            node_details[a_device] = {
                'level': a_level,
                'subnet': row['关联传输子网'],
                'city': row['地市名称'],
                'county': row['区/县名称']
            }
        
        # 查找所有Z端设备
        for z_num in [1, 2, 3]:  # Z1, Z2, Z3
            z_col = f'Z{z_num}'
            z_lv_col = f'Z{z_num}_LV'
            
            if z_col in df.columns and z_lv_col in df.columns:
                z_device = row[z_col]
                z_level = row[z_lv_col]
                
                if pd.notna(z_device) and pd.notna(z_level):
                    nodes[z_device] = z_level
                    connections.append((a_device, z_device))
                    
                    # 添加Z端设备详细信息（如果还没有）
                    if z_device not in node_details:
                        node_details[z_device] = {
                            'level': z_level,
                            'subnet': 'Unknown',
                            'city': 'Unknown',
                            'county': 'Unknown'
                        }
    
    print(f"发现 {len(nodes)} 个网元，{len(connections)} 个连接")
    
    # 创建网络图
    G = nx.Graph()
    
    # 添加节点
    for device, level in nodes.items():
        G.add_node(device, level=level)
    
    # 添加边
    for a, z in connections:
        if a in nodes and z in nodes:
            G.add_edge(a, z)
    
    # 使用spring布局算法自动排列节点
    pos = nx.spring_layout(G, k=4, iterations=100, seed=42)
    
    # 创建交互式图形
    fig, ax = plt.subplots(figsize=(16, 12))
    
    # 绘制连接线
    for edge in G.edges():
        x1, y1 = pos[edge[0]]
        x2, y2 = pos[edge[1]]
        ax.plot([x1, x2], [y1, y2], 'gray', linewidth=2, alpha=0.6, zorder=1)
    
    # 绘制节点
    node_patches = {}
    node_texts = {}
    
    for device, (x, y) in pos.items():
        level = nodes[device]
        
        # 根据设备级别选择颜色
        if level == '本地接入':
            color = '#e1f5fe'
            edge_color = '#01579b'
        elif level == '本地汇聚':
            color = '#e8f5e8'
            edge_color = '#2e7d32'
        else:
            color = 'lightgray'
            edge_color = 'black'
        
        # 简化设备名称显示
        device_short = device.split('-')[-1] if '-' in device else device
        if len(device_short) > 15:
            device_short = device_short[:12] + '...'
        
        # 绘制设备框
        width = 0.15
        height = 0.08
        
        rect = FancyBboxPatch(
            (x - width/2, y - height/2), width, height,
            boxstyle="round,pad=0.01",
            facecolor=color,
            edgecolor=edge_color,
            linewidth=2,
            zorder=2
        )
        ax.add_patch(rect)
        node_patches[device] = rect
        
        # 添加设备名称
        text = ax.text(x, y, device_short, 
                      ha='center', va='center', 
                      fontsize=8, weight='bold',
                      zorder=3)
        node_texts[device] = text
    
    # 添加点击事件处理
    def on_click(event):
        if event.inaxes != ax:
            return
        
        # 检查点击是否在某个节点上
        for device, (x, y) in pos.items():
            if abs(event.xdata - x) < 0.08 and abs(event.ydata - y) < 0.04:
                details = node_details[device]
                info = f"""设备信息：
设备名称: {device}
设备级别: {details['level']}
关联子网: {details['subnet']}
地市名称: {details['city']}
区/县名称: {details['county']}"""
                
                # 在控制台打印信息
                print(f"\n=== 点击设备信息 ===")
                print(info)
                
                # 高亮显示被点击的节点
                for dev, patch in node_patches.items():
                    if dev == device:
                        patch.set_facecolor('#fff3e0')
                        patch.set_edgecolor('#f57c00')
                        patch.set_linewidth(3)
                    else:
                        level = nodes[dev]
                        if level == '本地接入':
                            patch.set_facecolor('#e1f5fe')
                            patch.set_edgecolor('#01579b')
                        elif level == '本地汇聚':
                            patch.set_facecolor('#e8f5e8')
                            patch.set_edgecolor('#2e7d32')
                        patch.set_linewidth(2)
                
                plt.draw()
                break
    
    # 连接点击事件
    fig.canvas.mpl_connect('button_press_event', on_click)
    
    # 添加图例
    legend_elements = [
        patches.Rectangle((0, 0), 1, 1, facecolor='#e1f5fe', edgecolor='#01579b', label='本地接入 (10个)'),
        patches.Rectangle((0, 0), 1, 1, facecolor='#e8f5e8', edgecolor='#2e7d32', label='本地汇聚 (2个)'),
        patches.Rectangle((0, 0), 1, 1, facecolor='#fff3e0', edgecolor='#f57c00', label='选中状态')
    ]
    ax.legend(handles=legend_elements, loc='upper right', fontsize=10)
    
    # 设置标题和样式
    ax.set_title('苍南环32网络拓扑图 - 交互式版本\n(点击节点查看详细信息)', fontsize=16, weight='bold', pad=20)
    ax.axis('off')
    
    # 添加说明文字
    ax.text(0.02, 0.98, '交互说明：\n• 点击节点查看设备详细信息\n• 鼠标滚轮缩放\n• 拖拽移动视图', 
            transform=ax.transAxes, fontsize=10, verticalalignment='top',
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.7))
    
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('interactive_network_topology.png', dpi=300, bbox_inches='tight')
    print("交互式网络拓扑图已保存为: interactive_network_topology.png")
    
    # 显示图表
    plt.show()
    
    # 打印统计信息
    print(f"\n=== 网络统计信息 ===")
    level_count = {}
    for device, level in nodes.items():
        level_count[level] = level_count.get(level, 0) + 1
    
    for level, count in level_count.items():
        print(f"{level}: {count}个设备")
    
    print(f"总连接数: {len(connections)}")
    print("\n点击图表中的任意节点查看设备详细信息！")
    
    return G, nodes, connections

if __name__ == "__main__":
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    create_matplotlib_interactive()
