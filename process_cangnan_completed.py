import pandas as pd
import numpy as np

def process_cangnan_completed_file():
    """处理苍南环32_补充完成.xlsx文件，从F列开始补充数据"""
    
    # 读取所有Excel文件
    print("正在读取Excel文件...")
    df_cangnan = pd.read_excel('苍南环32_补充完成.xlsx')
    df_guanglu = pd.read_excel('光路-1.xlsx')
    df_wenzhou = pd.read_excel('温州PTN.xlsx')
    
    print(f"苍南环32_补充完成.xlsx: {len(df_cangnan)}行, {len(df_cangnan.columns)}列")
    print(f"光路-1.xlsx: {len(df_guanglu)}行")
    print(f"温州PTN.xlsx: {len(df_wenzhou)}行")
    
    # 显示当前文件结构
    print("\n当前文件列结构:")
    for i, col in enumerate(df_cangnan.columns):
        print(f"  {chr(65+i)}列: {col}")
    
    # 创建结果数据框，复制原始数据
    result_df = df_cangnan.copy()
    
    # 需要添加新列来存储GL、Z、Z_LV数据
    # 从F列开始（索引5），每3列为一组
    new_columns = []
    max_groups = 10  # 预估最多需要的组数
    
    for group in range(1, max_groups + 1):
        new_columns.extend([f'GL{group}', f'Z{group}', f'Z{group}_LV'])
    
    # 添加新列到结果数据框
    for col in new_columns:
        if col not in result_df.columns:
            result_df[col] = None
    
    print(f"\n添加了{len(new_columns)}个新列")
    
    # 处理每一行数据
    for idx, row in df_cangnan.iterrows():
        cangnan_a_value = row.iloc[0]  # A列值
        print(f"\n处理第{idx+1}行: {cangnan_a_value}")
        
        # 根据苍南环32_补充完成.xlsx中A列的值去匹配光路-1.xlsx的I列
        matches_i = df_guanglu[df_guanglu.iloc[:, 8] == cangnan_a_value]  # I列匹配
        print(f"  在光路-1.xlsx的I列找到{len(matches_i)}条匹配记录")
        
        group_num = 1
        
        # 处理I列匹配的数据
        for _, match_row in matches_i.iterrows():
            gl_value = match_row.iloc[0]  # A列值作为GL
            z_value = match_row.iloc[13]  # N列值作为Z
            
            # 从温州PTN.xlsx获取Z_LV
            z_lv = get_z_lv(df_wenzhou, z_value)
            
            # 填充数据到对应列
            gl_col = f'GL{group_num}'
            z_col = f'Z{group_num}'
            z_lv_col = f'Z{group_num}_LV'
            
            if gl_col in result_df.columns:
                result_df.loc[idx, gl_col] = gl_value
            if z_col in result_df.columns:
                result_df.loc[idx, z_col] = z_value
            if z_lv_col in result_df.columns:
                result_df.loc[idx, z_lv_col] = z_lv
            
            print(f"    第{group_num}组: GL{group_num}={gl_value}")
            print(f"              Z{group_num}={z_value}")
            print(f"              Z{group_num}_LV={z_lv}")
            
            group_num += 1
            
            # 限制最大组数
            if group_num > max_groups:
                print(f"    已达到最大组数限制({max_groups})，停止处理第{idx+1}行的后续数据")
                break
    
    # 移除空列（没有任何数据的列）
    cols_to_keep = []
    for col in result_df.columns:
        if result_df[col].notna().any():
            cols_to_keep.append(col)
    
    result_df = result_df[cols_to_keep]
    print(f"\n保留了{len(cols_to_keep)}列（移除了空列）")
    
    # 保存结果
    output_file = '苍南环32_F列补充最终版.xlsx'
    result_df.to_excel(output_file, index=False)
    print(f"\n数据处理完成，结果已保存到: {output_file}")
    
    # 显示处理结果摘要
    print("\n=== 处理结果摘要 ===")
    for idx in range(len(result_df)):
        row_name = result_df.iloc[idx, 0]
        groups_count = 0
        for group in range(1, max_groups + 1):
            gl_col = f'GL{group}'
            if gl_col in result_df.columns and pd.notna(result_df.loc[idx, gl_col]):
                groups_count += 1
            else:
                break
        print(f"第{idx+1}行: 补充了{groups_count}组GL/Z/Z_LV数据")
    
    return result_df

def get_z_lv(df_wenzhou, z_value):
    """从温州PTN.xlsx中获取Z_LV值"""
    if pd.isna(z_value):
        return None
    
    # 在温州PTN.xlsx的A列查找z_value
    matches = df_wenzhou[df_wenzhou.iloc[:, 0] == z_value]
    if len(matches) > 0:
        return matches.iloc[0, 1]  # 返回B列值
    return None

def analyze_current_file():
    """分析当前苍南环32_补充完成.xlsx文件结构"""
    try:
        df = pd.read_excel('苍南环32_补充完成.xlsx')
        
        print("=== 苍南环32_补充完成.xlsx 文件结构分析 ===")
        print(f"行数: {len(df)}")
        print(f"列数: {len(df.columns)}")
        
        print("\n所有列名:")
        for i, col in enumerate(df.columns):
            print(f"  {chr(65+i)}列: {col}")
        
        print("\n前3行数据预览:")
        print(df.head(3))
        
        print("\nA列数据:")
        for i, val in enumerate(df.iloc[:, 0]):
            print(f"  第{i+1}行: {val}")
            
    except Exception as e:
        print(f"读取苍南环32_补充完成.xlsx失败: {e}")

if __name__ == "__main__":
    # 先分析当前文件结构
    analyze_current_file()
    print("\n" + "="*60 + "\n")
    
    # 然后处理数据
    process_cangnan_completed_file()
