import pandas as pd
import numpy as np
from openpyxl import load_workbook

def process_cangnan_data():
    """根据规则处理苍南环32.xlsx数据"""

    # 读取所有Excel文件
    print("正在读取Excel文件...")
    df_cangnan = pd.read_excel('苍南环32.xlsx')
    df_guanglu = pd.read_excel('光路-1.xlsx')
    df_wenzhou = pd.read_excel('温州PTN.xlsx')

    print(f"苍南环32.xlsx: {len(df_cangnan)}行")
    print(f"光路-1.xlsx: {len(df_guanglu)}行")
    print(f"温州PTN.xlsx: {len(df_wenzhou)}行")

    # 创建结果数据框，复制原始数据
    result_df = df_cangnan.copy()

    # 处理每一行数据
    for idx, row in df_cangnan.iterrows():
        cangnan_a_value = row.iloc[0]  # A列值
        print(f"\n处理第{idx+1}行: {cangnan_a_value}")

        # 第一阶段：苍南环32.xlsx的A列匹配光路-1.xlsx的I列
        matches_i = df_guanglu[df_guanglu.iloc[:, 8] == cangnan_a_value]  # I列匹配
        print(f"  在光路-1.xlsx的I列找到{len(matches_i)}条匹配记录")

        group_num = 1
        col_offset = 16  # Q列开始的偏移量（Q=16）

        # 处理I列匹配的数据
        for _, match_row in matches_i.iterrows():
            gl_value = match_row.iloc[0]  # A列值作为gl
            remote_value = match_row.iloc[13]  # N列值作为remote

            # 从温州PTN.xlsx获取remote_lv
            remote_lv = get_remote_lv(df_wenzhou, remote_value)

            # 填充数据到对应列
            if col_offset < len(result_df.columns):
                result_df.iloc[idx, col_offset] = gl_value  # gl列
                if col_offset + 1 < len(result_df.columns):
                    result_df.iloc[idx, col_offset + 1] = remote_value  # remote列
                if col_offset + 2 < len(result_df.columns):
                    result_df.iloc[idx, col_offset + 2] = remote_lv  # remote_lv列

            print(f"    第{group_num}组: gl{group_num}={gl_value}, remote{group_num}={remote_value}, remote{group_num}_lv={remote_lv}")

            group_num += 1
            col_offset += 3

        # 第二阶段：苍南环32.xlsx的A列匹配光路-1.xlsx的N列
        matches_n = df_guanglu[df_guanglu.iloc[:, 13] == cangnan_a_value]  # N列匹配
        print(f"  在光路-1.xlsx的N列找到{len(matches_n)}条匹配记录")

        # 处理N列匹配的数据
        for _, match_row in matches_n.iterrows():
            gl_value = match_row.iloc[0]  # A列值作为gl
            remote_value = match_row.iloc[8]  # I列值作为remote

            # 从温州PTN.xlsx获取remote_lv
            remote_lv = get_remote_lv(df_wenzhou, remote_value)

            # 填充数据到对应列
            if col_offset < len(result_df.columns):
                result_df.iloc[idx, col_offset] = gl_value  # gl列
                if col_offset + 1 < len(result_df.columns):
                    result_df.iloc[idx, col_offset + 1] = remote_value  # remote列
                if col_offset + 2 < len(result_df.columns):
                    result_df.iloc[idx, col_offset + 2] = remote_lv  # remote_lv列

            print(f"    第{group_num}组: gl{group_num}={gl_value}, remote{group_num}={remote_value}, remote{group_num}_lv={remote_lv}")

            group_num += 1
            col_offset += 3

    # 保存结果
    output_file = '苍南环32_补充完成.xlsx'
    result_df.to_excel(output_file, index=False)
    print(f"\n数据处理完成，结果已保存到: {output_file}")

    return result_df

def get_remote_lv(df_wenzhou, remote_value):
    """从温州PTN.xlsx中获取remote_lv值"""
    if pd.isna(remote_value):
        return None

    # 在温州PTN.xlsx的A列查找remote_value
    matches = df_wenzhou[df_wenzhou.iloc[:, 0] == remote_value]
    if len(matches) > 0:
        return matches.iloc[0, 1]  # 返回B列值
    return None

if __name__ == "__main__":
    process_cangnan_data()
