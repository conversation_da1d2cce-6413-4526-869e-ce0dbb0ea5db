import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import networkx as nx
from matplotlib.patches import FancyBboxPatch, Circle, ConnectionPatch
import numpy as np
import matplotlib.patheffects as path_effects

class BeautifulNetworkTopology:
    def __init__(self):
        self.nodes = {}
        self.connections = []
        self.node_details = {}
        self.pos = {}
        self.node_patches = {}
        self.node_texts = {}
        self.edge_lines = []
        self.selected_node = None
        self.dragging = False
        self.offset_x = 0
        self.offset_y = 0
        
        # 美化配色方案
        self.colors = {
            'access': {'fill': '#4FC3F7', 'edge': '#0277BD', 'text': '#FFFFFF'},
            'aggregation': {'fill': '#81C784', 'edge': '#388E3C', 'text': '#FFFFFF'},
            'selected': {'fill': '#FFB74D', 'edge': '#F57C00', 'text': '#FFFFFF'},
            'background': '#F5F5F5',
            'edge': '#90A4AE',
            'edge_highlight': '#FF7043'
        }
        
    def load_data(self):
        """加载网络数据"""
        print("读取文件: cnh_20250805_162949.xlsx")
        
        df = pd.read_excel('cnh_20250805_162949.xlsx')
        
        for idx, row in df.iterrows():
            a_device = row['A1']
            a_level = row['A1_LV']
            
            if pd.notna(a_device):
                self.nodes[a_device] = a_level
                self.node_details[a_device] = {
                    'level': a_level,
                    'subnet': row['关联传输子网'],
                    'city': row['地市名称'],
                    'county': row['区/县名称']
                }
            
            for z_num in [1, 2, 3]:
                z_col = f'Z{z_num}'
                z_lv_col = f'Z{z_num}_LV'
                
                if z_col in df.columns and z_lv_col in df.columns:
                    z_device = row[z_col]
                    z_level = row[z_lv_col]
                    
                    if pd.notna(z_device) and pd.notna(z_level):
                        self.nodes[z_device] = z_level
                        self.connections.append((a_device, z_device))
                        
                        if z_device not in self.node_details:
                            self.node_details[z_device] = {
                                'level': z_level,
                                'subnet': 'Unknown',
                                'city': 'Unknown',
                                'county': 'Unknown'
                            }
        
        print(f"发现 {len(self.nodes)} 个网元，{len(self.connections)} 个连接")
        
        # 使用更好的布局算法
        G = nx.Graph()
        for device in self.nodes.keys():
            G.add_node(device)
        for a, z in self.connections:
            if a in self.nodes and z in self.nodes:
                G.add_edge(a, z)
        
        # 使用spring布局算法（不需要scipy）
        self.pos = nx.spring_layout(G, k=3, iterations=100, seed=42, scale=2)
        
    def create_plot(self):
        """创建美观的网络图"""
        # 使用深色主题
        plt.style.use('default')
        self.fig, self.ax = plt.subplots(figsize=(18, 14))
        self.fig.patch.set_facecolor(self.colors['background'])
        self.ax.set_facecolor(self.colors['background'])
        
        # 绘制连接线（带阴影效果）
        self.draw_edges_beautiful()
        
        # 绘制节点
        self.draw_nodes_beautiful()
        
        # 连接事件处理器
        self.fig.canvas.mpl_connect('button_press_event', self.on_press)
        self.fig.canvas.mpl_connect('button_release_event', self.on_release)
        self.fig.canvas.mpl_connect('motion_notify_event', self.on_motion)
        
        # 添加美观的图例和说明
        self.add_beautiful_legend()
        
        # 设置美观的标题
        title = self.ax.text(0.5, 0.95, '苍南环32网络拓扑图', 
                           transform=self.ax.transAxes,
                           fontsize=24, weight='bold', 
                           ha='center', va='center',
                           color='#263238')
        title.set_path_effects([path_effects.withStroke(linewidth=3, foreground='white')])
        
        subtitle = self.ax.text(0.5, 0.92, '可拖拽交互式网络架构图', 
                              transform=self.ax.transAxes,
                              fontsize=14, style='italic',
                              ha='center', va='center',
                              color='#546E7A')
        
        self.ax.axis('off')
        plt.tight_layout()
        
    def draw_edges_beautiful(self):
        """绘制美观的连接线"""
        for line in self.edge_lines:
            line.remove()
        self.edge_lines.clear()
        
        for a_device, z_device in self.connections:
            if a_device in self.pos and z_device in self.pos:
                x1, y1 = self.pos[a_device]
                x2, y2 = self.pos[z_device]
                
                # 绘制阴影线
                shadow_line, = self.ax.plot([x1+0.005, x2+0.005], [y1-0.005, y2-0.005], 
                                          color='black', linewidth=4, alpha=0.2, zorder=1)
                self.edge_lines.append(shadow_line)
                
                # 绘制主线
                main_line, = self.ax.plot([x1, x2], [y1, y2], 
                                        color=self.colors['edge'], linewidth=3, 
                                        alpha=0.8, zorder=2)
                self.edge_lines.append(main_line)
    
    def draw_nodes_beautiful(self):
        """绘制美观的节点"""
        for device, (x, y) in self.pos.items():
            level = self.nodes[device]
            
            # 选择颜色方案
            if level == '本地接入':
                color_scheme = self.colors['access']
                node_shape = 'round'
            elif level == '本地汇聚':
                color_scheme = self.colors['aggregation']
                node_shape = 'round'
            else:
                color_scheme = {'fill': '#BDBDBD', 'edge': '#757575', 'text': '#FFFFFF'}
                node_shape = 'round'
            
            # 获取设备显示信息
            device_info = self.get_beautiful_device_info(device)
            
            # 计算节点大小
            radius = 0.12 if level == '本地汇聚' else 0.10
            
            # 绘制阴影
            shadow = Circle((x+0.01, y-0.01), radius, 
                          facecolor='black', alpha=0.3, zorder=3)
            self.ax.add_patch(shadow)
            
            # 绘制主节点
            if level == '本地汇聚':
                # 汇聚节点使用六边形
                angles = np.linspace(0, 2*np.pi, 7)
                hex_x = x + radius * np.cos(angles)
                hex_y = y + radius * np.sin(angles)
                node = patches.Polygon(list(zip(hex_x, hex_y)), 
                                     facecolor=color_scheme['fill'],
                                     edgecolor=color_scheme['edge'],
                                     linewidth=3, zorder=4)
            else:
                # 接入节点使用圆形
                node = Circle((x, y), radius, 
                            facecolor=color_scheme['fill'],
                            edgecolor=color_scheme['edge'],
                            linewidth=3, zorder=4)
            
            self.ax.add_patch(node)
            self.node_patches[device] = node
            
            # 添加设备标签（带发光效果）
            text = self.ax.text(x, y, device_info['short'], 
                              ha='center', va='center', 
                              fontsize=9, weight='bold',
                              color=color_scheme['text'], zorder=5)
            text.set_path_effects([path_effects.withStroke(linewidth=2, foreground=color_scheme['edge'])])
            self.node_texts[device] = text
            
            # 添加设备详细标签
            detail_text = self.ax.text(x, y-radius-0.08, device_info['detail'], 
                                     ha='center', va='center', 
                                     fontsize=7, style='italic',
                                     color='#37474F', zorder=5,
                                     bbox=dict(boxstyle="round,pad=0.3", 
                                             facecolor='white', 
                                             edgecolor=color_scheme['edge'],
                                             alpha=0.9))
    
    def get_beautiful_device_info(self, device):
        """获取美观的设备显示信息"""
        if '-' in device:
            parts = device.split('-')
            if len(parts) >= 4:
                device_id = parts[1]
                location = parts[2]
                device_type = parts[3]
                
                # 简化位置名称
                location_short = location
                replacements = {
                    '温州苍南甬台温福高铁': '高铁',
                    '温州苍南': '',
                    '温州市苍南': '',
                    '苍南桥墩': '桥墩',
                    '苍南': '',
                    '隧道南口': '隧道',
                    '隧道南': '隧道'
                }
                
                for old, new in replacements.items():
                    location_short = location_short.replace(old, new)
                
                if len(location_short) > 8:
                    location_short = location_short[:6] + '..'
                
                return {
                    'short': f"{device_id}\n{device_type}",
                    'detail': location_short,
                    'full': device
                }
            else:
                return {
                    'short': parts[-1],
                    'detail': '',
                    'full': device
                }
        else:
            return {
                'short': device,
                'detail': '',
                'full': device
            }
    
    def find_node_at_position(self, x, y):
        """查找指定位置的节点"""
        for device, (node_x, node_y) in self.pos.items():
            level = self.nodes[device]
            radius = 0.12 if level == '本地汇聚' else 0.10
            
            distance = np.sqrt((x - node_x)**2 + (y - node_y)**2)
            if distance < radius:
                return device
        return None
    
    def on_press(self, event):
        """鼠标按下事件"""
        if event.inaxes != self.ax:
            return
        
        clicked_node = self.find_node_at_position(event.xdata, event.ydata)
        
        if clicked_node:
            if event.button == 1:
                if event.dblclick:
                    self.show_node_info(clicked_node)
                else:
                    self.selected_node = clicked_node
                    self.dragging = True
                    node_x, node_y = self.pos[clicked_node]
                    self.offset_x = event.xdata - node_x
                    self.offset_y = event.ydata - node_y
                    self.highlight_node(clicked_node)
    
    def on_release(self, event):
        """鼠标释放事件"""
        if self.dragging:
            self.dragging = False
            self.selected_node = None
            print(f"节点移动完成")
    
    def on_motion(self, event):
        """鼠标移动事件"""
        if self.dragging and self.selected_node and event.inaxes == self.ax:
            new_x = event.xdata - self.offset_x
            new_y = event.ydata - self.offset_y
            
            self.pos[self.selected_node] = (new_x, new_y)
            self.update_node_position(self.selected_node, new_x, new_y)
            self.draw_edges_beautiful()
            self.fig.canvas.draw_idle()
    
    def update_node_position(self, device, x, y):
        """更新节点位置"""
        level = self.nodes[device]
        radius = 0.12 if level == '本地汇聚' else 0.10
        
        if device in self.node_patches:
            patch = self.node_patches[device]
            if hasattr(patch, 'center'):  # Circle
                patch.center = (x, y)
            else:  # Polygon
                angles = np.linspace(0, 2*np.pi, 7)
                hex_x = x + radius * np.cos(angles)
                hex_y = y + radius * np.sin(angles)
                patch.set_xy(list(zip(hex_x, hex_y)))
        
        if device in self.node_texts:
            self.node_texts[device].set_position((x, y))
    
    def show_node_info(self, device):
        """显示节点详细信息"""
        details = self.node_details[device]
        info = f"""设备信息：
设备名称: {device}
设备级别: {details['level']}
关联子网: {details['subnet']}
地市名称: {details['city']}
区/县名称: {details['county']}"""
        
        print(f"\n=== 设备详细信息 ===")
        print(info)
        self.highlight_node(device, temp=True)
    
    def highlight_node(self, device, temp=False):
        """高亮显示节点"""
        for dev, patch in self.node_patches.items():
            if dev == device:
                if temp:
                    patch.set_facecolor('#FFF59D')
                    patch.set_edgecolor('#F9A825')
                else:
                    patch.set_facecolor(self.colors['selected']['fill'])
                    patch.set_edgecolor(self.colors['selected']['edge'])
                patch.set_linewidth(4)
            else:
                level = self.nodes[dev]
                if level == '本地接入':
                    patch.set_facecolor(self.colors['access']['fill'])
                    patch.set_edgecolor(self.colors['access']['edge'])
                elif level == '本地汇聚':
                    patch.set_facecolor(self.colors['aggregation']['fill'])
                    patch.set_edgecolor(self.colors['aggregation']['edge'])
                patch.set_linewidth(3)
        
        self.fig.canvas.draw_idle()
    
    def add_beautiful_legend(self):
        """添加美观的图例"""
        # 创建自定义图例
        legend_elements = [
            patches.Circle((0, 0), 0.5, facecolor=self.colors['access']['fill'],
                         edgecolor=self.colors['access']['edge'], linewidth=2, label='本地接入设备'),
            patches.RegularPolygon((0, 0), 6, radius=0.5, facecolor=self.colors['aggregation']['fill'],
                                 edgecolor=self.colors['aggregation']['edge'], linewidth=2, label='本地汇聚设备'),
            patches.Circle((0, 0), 0.5, facecolor=self.colors['selected']['fill'],
                         edgecolor=self.colors['selected']['edge'], linewidth=2, label='选中状态')
        ]
        
        legend = self.ax.legend(handles=legend_elements, loc='upper left', 
                              fontsize=11, frameon=True, fancybox=True, shadow=True)
        legend.get_frame().set_facecolor('white')
        legend.get_frame().set_alpha(0.9)
        
        # 添加操作说明
        instructions = """🎯 操作指南
• 拖拽节点：单击并拖动改变位置
• 查看详情：双击节点显示设备信息  
• 缩放视图：鼠标滚轮放大缩小
• 移动视图：拖拽空白区域

💡 设计说明
• 圆形：本地接入设备
• 六边形：本地汇聚设备
• 连接线自动跟随节点移动"""
        
        self.ax.text(0.02, 0.35, instructions, 
                    transform=self.ax.transAxes, fontsize=10, 
                    verticalalignment='top',
                    bbox=dict(boxstyle="round,pad=0.5", 
                            facecolor='white', 
                            edgecolor='#2196F3',
                            alpha=0.95, linewidth=2))
    
    def save_beautiful_layout(self):
        """保存美观的布局"""
        plt.savefig('beautiful_network_topology.png', dpi=300, bbox_inches='tight',
                   facecolor=self.colors['background'])
        print("美观网络拓扑图已保存为: beautiful_network_topology.png")
    
    def run(self):
        """运行美观的交互式图表"""
        self.load_data()
        self.create_plot()
        self.save_beautiful_layout()
        
        print("\n🎨 美观网络拓扑图已启动")
        print("✨ 特色功能：现代化设计、流畅交互、美观配色")
        
        plt.show()

def create_beautiful_topology():
    """创建美观的网络拓扑图"""
    topology = BeautifulNetworkTopology()
    topology.run()
    return topology

if __name__ == "__main__":
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    create_beautiful_topology()
