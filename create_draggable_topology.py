import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import networkx as nx
from matplotlib.patches import FancyBboxPatch
import numpy as np

class DraggableNetworkTopology:
    def __init__(self):
        self.nodes = {}
        self.connections = []
        self.node_details = {}
        self.pos = {}
        self.node_patches = {}
        self.node_texts = {}
        self.edge_lines = []
        self.selected_node = None
        self.dragging = False
        self.offset_x = 0
        self.offset_y = 0
        
    def load_data(self):
        """加载网络数据"""
        print("读取文件: cnh_20250805_162949.xlsx")
        
        # 读取数据
        df = pd.read_excel('cnh_20250805_162949.xlsx')
        
        for idx, row in df.iterrows():
            a_device = row['A1']
            a_level = row['A1_LV']
            
            # 添加A端设备
            if pd.notna(a_device):
                self.nodes[a_device] = a_level
                self.node_details[a_device] = {
                    'level': a_level,
                    'subnet': row['关联传输子网'],
                    'city': row['地市名称'],
                    'county': row['区/县名称']
                }
            
            # 查找所有Z端设备
            for z_num in [1, 2, 3]:  # Z1, Z2, Z3
                z_col = f'Z{z_num}'
                z_lv_col = f'Z{z_num}_LV'
                
                if z_col in df.columns and z_lv_col in df.columns:
                    z_device = row[z_col]
                    z_level = row[z_lv_col]
                    
                    if pd.notna(z_device) and pd.notna(z_level):
                        self.nodes[z_device] = z_level
                        self.connections.append((a_device, z_device))
                        
                        if z_device not in self.node_details:
                            self.node_details[z_device] = {
                                'level': z_level,
                                'subnet': 'Unknown',
                                'city': 'Unknown',
                                'county': 'Unknown'
                            }
        
        print(f"发现 {len(self.nodes)} 个网元，{len(self.connections)} 个连接")
        
        # 使用NetworkX生成初始布局
        G = nx.Graph()
        for device in self.nodes.keys():
            G.add_node(device)
        for a, z in self.connections:
            if a in self.nodes and z in self.nodes:
                G.add_edge(a, z)
        
        self.pos = nx.spring_layout(G, k=4, iterations=100, seed=42)
        
    def create_plot(self):
        """创建可拖拽的网络图"""
        self.fig, self.ax = plt.subplots(figsize=(16, 12))
        
        # 绘制连接线
        self.draw_edges()
        
        # 绘制节点
        self.draw_nodes()
        
        # 连接事件处理器
        self.fig.canvas.mpl_connect('button_press_event', self.on_press)
        self.fig.canvas.mpl_connect('button_release_event', self.on_release)
        self.fig.canvas.mpl_connect('motion_notify_event', self.on_motion)
        
        # 添加图例和说明
        self.add_legend_and_instructions()
        
        # 设置标题
        self.ax.set_title('苍南环32网络拓扑图 - 可拖拽版本\n(拖拽节点移动位置，点击查看详细信息)', 
                         fontsize=16, weight='bold', pad=20)
        self.ax.axis('off')
        
        plt.tight_layout()
        
    def draw_edges(self):
        """绘制连接线"""
        # 清除旧的连接线
        for line in self.edge_lines:
            line.remove()
        self.edge_lines.clear()
        
        # 绘制新的连接线
        for a_device, z_device in self.connections:
            if a_device in self.pos and z_device in self.pos:
                x1, y1 = self.pos[a_device]
                x2, y2 = self.pos[z_device]
                line, = self.ax.plot([x1, x2], [y1, y2], 'gray', linewidth=2, alpha=0.6, zorder=1)
                self.edge_lines.append(line)
    
    def draw_nodes(self):
        """绘制节点"""
        for device, (x, y) in self.pos.items():
            level = self.nodes[device]

            # 根据设备级别选择颜色
            if level == '本地接入':
                color = '#e1f5fe'
                edge_color = '#01579b'
            elif level == '本地汇聚':
                color = '#e8f5e8'
                edge_color = '#2e7d32'
            else:
                color = 'lightgray'
                edge_color = 'black'

            # 提取设备关键信息用于显示
            device_display = self.get_device_display_name(device)

            # 绘制设备框 - 根据文本长度调整大小
            text_lines = device_display.split('\n')
            width = max(0.2, len(max(text_lines, key=len)) * 0.012)
            height = max(0.08, len(text_lines) * 0.03)

            rect = FancyBboxPatch(
                (x - width/2, y - height/2), width, height,
                boxstyle="round,pad=0.01",
                facecolor=color,
                edgecolor=edge_color,
                linewidth=2,
                zorder=2,
                picker=True  # 使节点可选择
            )
            self.ax.add_patch(rect)
            self.node_patches[device] = rect

            # 添加设备名称 - 多行显示
            text = self.ax.text(x, y, device_display,
                              ha='center', va='center',
                              fontsize=7, weight='bold',
                              zorder=3, picker=True,
                              bbox=dict(boxstyle="round,pad=0.2",
                                       facecolor='white', alpha=0.8))
            self.node_texts[device] = text

    def get_device_display_name(self, device):
        """获取设备显示名称"""
        # 提取设备关键信息
        if '-' in device:
            parts = device.split('-')
            if len(parts) >= 4:
                # 格式: 176-29148-温州苍南甬台温福高铁仙堂隧道南口-PTN970C
                device_id = parts[1]  # 29148
                location = parts[2]   # 温州苍南甬台温福高铁仙堂隧道南口
                device_type = parts[3] # PTN970C

                # 简化地点名称
                if '温州' in location:
                    location = location.replace('温州苍南甬台温福高铁', '').replace('温州苍南', '').replace('温州市苍南', '')
                    location = location.replace('苍南桥墩', '').replace('苍南', '')

                # 构建显示名称
                display_name = f"{device_id}\n{location}\n{device_type}"
                return display_name
            else:
                # 简单格式处理
                return device.split('-')[-1]
        else:
            return device
    
    def find_node_at_position(self, x, y):
        """查找指定位置的节点"""
        for device, (node_x, node_y) in self.pos.items():
            # 根据设备名称长度调整检测范围
            device_display = self.get_device_display_name(device)
            text_lines = device_display.split('\n')
            width = max(0.2, len(max(text_lines, key=len)) * 0.012)
            height = max(0.08, len(text_lines) * 0.03)

            if abs(x - node_x) < width/2 and abs(y - node_y) < height/2:
                return device
        return None
    
    def on_press(self, event):
        """鼠标按下事件"""
        if event.inaxes != self.ax:
            return
        
        # 查找点击的节点
        clicked_node = self.find_node_at_position(event.xdata, event.ydata)
        
        if clicked_node:
            if event.button == 1:  # 左键点击
                if event.dblclick:  # 双击显示信息
                    self.show_node_info(clicked_node)
                else:  # 单击开始拖拽
                    self.selected_node = clicked_node
                    self.dragging = True
                    node_x, node_y = self.pos[clicked_node]
                    self.offset_x = event.xdata - node_x
                    self.offset_y = event.ydata - node_y
                    
                    # 高亮选中的节点
                    self.highlight_node(clicked_node)
    
    def on_release(self, event):
        """鼠标释放事件"""
        if self.dragging:
            self.dragging = False
            self.selected_node = None
            print(f"节点移动完成，新位置已保存")
    
    def on_motion(self, event):
        """鼠标移动事件"""
        if self.dragging and self.selected_node and event.inaxes == self.ax:
            # 更新节点位置
            new_x = event.xdata - self.offset_x
            new_y = event.ydata - self.offset_y
            
            self.pos[self.selected_node] = (new_x, new_y)
            
            # 更新节点显示
            self.update_node_position(self.selected_node, new_x, new_y)
            
            # 重新绘制连接线
            self.draw_edges()
            
            # 刷新显示
            self.fig.canvas.draw_idle()
    
    def update_node_position(self, device, x, y):
        """更新节点位置"""
        # 更新节点框位置
        if device in self.node_patches:
            patch = self.node_patches[device]
            # 重新计算节点大小
            device_display = self.get_device_display_name(device)
            text_lines = device_display.split('\n')
            width = max(0.2, len(max(text_lines, key=len)) * 0.012)
            height = max(0.08, len(text_lines) * 0.03)

            patch.set_x(x - width/2)
            patch.set_y(y - height/2)
            patch.set_width(width)
            patch.set_height(height)

        # 更新文本位置
        if device in self.node_texts:
            text = self.node_texts[device]
            text.set_position((x, y))
    
    def show_node_info(self, device):
        """显示节点详细信息"""
        details = self.node_details[device]
        info = f"""设备信息：
设备名称: {device}
设备级别: {details['level']}
关联子网: {details['subnet']}
地市名称: {details['city']}
区/县名称: {details['county']}"""
        
        print(f"\n=== 双击设备信息 ===")
        print(info)
        
        # 临时高亮显示
        self.highlight_node(device, temp=True)
    
    def highlight_node(self, device, temp=False):
        """高亮显示节点"""
        for dev, patch in self.node_patches.items():
            if dev == device:
                if temp:
                    patch.set_facecolor('#fff9c4')
                    patch.set_edgecolor('#f57f17')
                else:
                    patch.set_facecolor('#fff3e0')
                    patch.set_edgecolor('#f57c00')
                patch.set_linewidth(3)
            else:
                level = self.nodes[dev]
                if level == '本地接入':
                    patch.set_facecolor('#e1f5fe')
                    patch.set_edgecolor('#01579b')
                elif level == '本地汇聚':
                    patch.set_facecolor('#e8f5e8')
                    patch.set_edgecolor('#2e7d32')
                patch.set_linewidth(2)
        
        self.fig.canvas.draw_idle()
    
    def add_legend_and_instructions(self):
        """添加图例和操作说明"""
        # 添加图例
        legend_elements = [
            patches.Rectangle((0, 0), 1, 1, facecolor='#e1f5fe', edgecolor='#01579b', label='本地接入 (10个)'),
            patches.Rectangle((0, 0), 1, 1, facecolor='#e8f5e8', edgecolor='#2e7d32', label='本地汇聚 (2个)'),
            patches.Rectangle((0, 0), 1, 1, facecolor='#fff3e0', edgecolor='#f57c00', label='选中/拖拽中')
        ]
        self.ax.legend(handles=legend_elements, loc='upper right', fontsize=10)
        
        # 添加操作说明
        instructions = """操作说明：
• 单击并拖拽：移动节点位置
• 双击节点：查看设备详细信息
• 鼠标滚轮：缩放图表
• 拖拽空白区域：移动视图
• 连接线会自动跟随节点移动"""
        
        self.ax.text(0.02, 0.98, instructions, 
                    transform=self.ax.transAxes, fontsize=10, verticalalignment='top',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.8))
    
    def save_layout(self):
        """保存当前布局"""
        plt.savefig('draggable_network_topology.png', dpi=300, bbox_inches='tight')
        print("可拖拽网络拓扑图已保存为: draggable_network_topology.png")
    
    def run(self):
        """运行交互式图表"""
        self.load_data()
        self.create_plot()
        self.save_layout()
        
        print("\n=== 可拖拽网络拓扑图已启动 ===")
        print("操作方式：")
        print("• 单击并拖拽节点：自由移动网元位置")
        print("• 双击节点：查看设备详细信息")
        print("• 连接线会自动跟随节点移动")
        print("• 关闭窗口前的布局会自动保存")
        
        plt.show()

def create_draggable_topology():
    """创建可拖拽的网络拓扑图"""
    topology = DraggableNetworkTopology()
    topology.run()
    return topology

if __name__ == "__main__":
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    create_draggable_topology()
