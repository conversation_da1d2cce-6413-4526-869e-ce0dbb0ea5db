import pandas as pd
import numpy as np

def process_f_column_data():
    """从F列开始补充数据，按照新的规则处理"""

    # 读取所有Excel文件
    print("正在读取Excel文件...")
    df_cangnan = pd.read_excel('苍南环32.xlsx')  # 使用原始文件
    df_guanglu = pd.read_excel('光路-1.xlsx')
    df_wenzhou = pd.read_excel('温州PTN.xlsx')
    
    print(f"苍南环32.xlsx: {len(df_cangnan)}行, {len(df_cangnan.columns)}列")
    print(f"光路-1.xlsx: {len(df_guanglu)}行")
    print(f"温州PTN.xlsx: {len(df_wenzhou)}行")
    
    # 显示当前F列开始的列名
    print("\n当前F列开始的列结构:")
    for i in range(5, min(len(df_cangnan.columns), 15)):  # F列是第5列(索引5)
        print(f"  {chr(65+i)}列: {df_cangnan.columns[i]}")
    
    # 创建结果数据框，复制原始数据
    result_df = df_cangnan.copy()
    
    # 处理每一行数据
    for idx, row in df_cangnan.iterrows():
        cangnan_a_value = row.iloc[0]  # A列值
        print(f"\n处理第{idx+1}行: {cangnan_a_value}")
        
        # 根据苍南环32_补充完成.xlsx中A列的值去匹配光路-1.xlsx的I列
        matches_i = df_guanglu[df_guanglu.iloc[:, 8] == cangnan_a_value]  # I列匹配
        print(f"  在光路-1.xlsx的I列找到{len(matches_i)}条匹配记录")
        
        group_num = 1
        col_offset = 5  # F列开始的偏移量（F=5）
        
        # 处理I列匹配的数据
        for _, match_row in matches_i.iterrows():
            gl_value = match_row.iloc[0]  # A列值作为GL
            z_value = match_row.iloc[13]  # N列值作为Z
            
            # 从温州PTN.xlsx获取Z_LV
            z_lv = get_z_lv(df_wenzhou, z_value)
            
            # 填充数据到对应列
            if col_offset < len(result_df.columns):
                result_df.iloc[idx, col_offset] = gl_value  # GL列
                if col_offset + 1 < len(result_df.columns):
                    result_df.iloc[idx, col_offset + 1] = z_value  # Z列
                if col_offset + 2 < len(result_df.columns):
                    result_df.iloc[idx, col_offset + 2] = z_lv  # Z_LV列
            
            print(f"    第{group_num}组: GL{group_num}={gl_value}")
            print(f"              Z{group_num}={z_value}")
            print(f"              Z{group_num}_LV={z_lv}")
            
            group_num += 1
            col_offset += 3
            
            # 如果超出现有列数，停止处理
            if col_offset >= len(result_df.columns):
                print(f"    已达到列数限制，停止处理第{idx+1}行的后续数据")
                break
    
    # 保存结果
    output_file = '苍南环32_F列补充完成_new.xlsx'
    result_df.to_excel(output_file, index=False)
    print(f"\n数据处理完成，结果已保存到: {output_file}")

    # 显示处理结果摘要
    print("\n=== 处理结果摘要 ===")
    for idx in range(len(result_df)):
        row_name = result_df.iloc[idx, 0]
        groups_count = 0
        col_start = 5  # F列
        for col in range(col_start, len(result_df.columns), 3):
            if col + 2 < len(result_df.columns):
                gl_val = result_df.iloc[idx, col]
                if pd.notna(gl_val):
                    groups_count += 1
                else:
                    break
        print(f"第{idx+1}行: 从F列开始补充了{groups_count}组数据")
    
    return result_df

def get_z_lv(df_wenzhou, z_value):
    """从温州PTN.xlsx中获取Z_LV值"""
    if pd.isna(z_value):
        return None
    
    # 在温州PTN.xlsx的A列查找z_value
    matches = df_wenzhou[df_wenzhou.iloc[:, 0] == z_value]
    if len(matches) > 0:
        return matches.iloc[0, 1]  # 返回B列值
    return None

def analyze_current_structure():
    """分析当前文件结构"""
    df = pd.read_excel('苍南环32.xlsx')
    
    print("=== 当前文件结构分析 ===")
    print(f"行数: {len(df)}")
    print(f"列数: {len(df.columns)}")
    
    print("\n所有列名:")
    for i, col in enumerate(df.columns):
        print(f"  {chr(65+i)}列: {col}")
    
    print("\n前3行数据预览:")
    print(df.head(3))
    
    print("\nF列开始的数据情况:")
    for i in range(5, min(len(df.columns), 15)):  # F列开始
        col_name = df.columns[i]
        non_null_count = df.iloc[:, i].notna().sum()
        print(f"  {chr(65+i)}列({col_name}): {non_null_count}个非空值")

if __name__ == "__main__":
    # 先分析当前结构
    analyze_current_structure()
    print("\n" + "="*60 + "\n")
    
    # 然后处理数据
    process_f_column_data()
