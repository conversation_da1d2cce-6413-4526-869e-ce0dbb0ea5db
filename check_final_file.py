import pandas as pd

def check_final_file():
    """检查最终生成的文件内容"""
    
    try:
        df = pd.read_excel('苍南环32_F列补充最终版.xlsx')
        
        print("=== 文件详细信息 ===")
        print(f"行数: {len(df)}")
        print(f"列数: {len(df.columns)}")
        
        print("\n=== 所有列名 ===")
        for i, col in enumerate(df.columns):
            print(f"  {chr(65+i)}列: {col}")
        
        print("\n=== 完整数据预览 ===")
        print(df.to_string())
        
        print("\n=== 检查F列之后的数据 ===")
        if len(df.columns) > 5:
            print("F列之后的列:")
            for i in range(5, len(df.columns)):
                col_name = df.columns[i]
                print(f"  {chr(65+i)}列({col_name}): 有数据的行数 = {df.iloc[:, i].notna().sum()}")
                
                # 显示前几个非空值
                non_null_values = df.iloc[:, i].dropna()
                if len(non_null_values) > 0:
                    print(f"    前3个值: {non_null_values.head(3).tolist()}")
        else:
            print("文件只有5列，没有补充数据！")
            
    except Exception as e:
        print(f"读取文件失败: {e}")

if __name__ == "__main__":
    check_final_file()
