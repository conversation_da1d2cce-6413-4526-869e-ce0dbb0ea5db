import pandas as pd

def check_cnh_structure():
    """检查cnh文件的结构"""
    
    try:
        df = pd.read_excel('cnh_20250805_162949.xlsx')
        
        print(f"文件行数: {len(df)}")
        print(f"文件列数: {len(df.columns)}")
        
        print("\n所有列名:")
        for i, col in enumerate(df.columns):
            print(f"  {i}: {col}")
        
        print("\n前3行数据:")
        print(df.head(3))
        
        print("\n各列的数据类型:")
        print(df.dtypes)
        
    except Exception as e:
        print(f"读取文件失败: {e}")

if __name__ == "__main__":
    check_cnh_structure()
