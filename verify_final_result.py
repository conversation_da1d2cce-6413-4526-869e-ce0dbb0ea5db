import pandas as pd

def verify_final_result():
    """验证最终处理结果"""
    
    # 读取原始文件和最终结果文件
    df_original = pd.read_excel('苍南环32_补充完成.xlsx')
    df_result = pd.read_excel('苍南环32_F列补充最终版.xlsx')
    
    print("=== 验证最终处理结果 ===")
    print(f"原始文件行数: {len(df_original)}")
    print(f"结果文件行数: {len(df_result)}")
    print(f"原始文件列数: {len(df_original.columns)}")
    print(f"结果文件列数: {len(df_result.columns)}")
    
    print("\n=== 最终文件列结构 ===")
    for i, col in enumerate(df_result.columns):
        print(f"  {chr(65+i)}列: {col}")
    
    print("\n=== 补充的GL/Z/Z_LV数据详情 ===")
    
    for idx in range(len(df_result)):
        row_name = df_result.iloc[idx, 0]  # A列的设备名称
        print(f"\n第{idx+1}行 ({row_name}):")
        
        # 查找GL、Z、Z_LV列
        group_num = 1
        found_data = False
        
        while True:
            gl_col = f'GL{group_num}'
            z_col = f'Z{group_num}'
            z_lv_col = f'Z{group_num}_LV'
            
            if gl_col in df_result.columns:
                gl_val = df_result.loc[idx, gl_col]
                z_val = df_result.loc[idx, z_col] if z_col in df_result.columns else None
                z_lv_val = df_result.loc[idx, z_lv_col] if z_lv_col in df_result.columns else None
                
                if pd.notna(gl_val):
                    print(f"  第{group_num}组: GL{group_num}={gl_val}")
                    print(f"           Z{group_num}={z_val}")
                    print(f"           Z{group_num}_LV={z_lv_val}")
                    found_data = True
                    group_num += 1
                else:
                    break
            else:
                break
        
        if not found_data:
            print("  未找到匹配数据")
    
    print("\n=== 数据统计 ===")
    total_groups = 0
    for idx in range(len(df_result)):
        row_name = df_result.iloc[idx, 0]
        groups_count = 0
        
        group_num = 1
        while True:
            gl_col = f'GL{group_num}'
            if gl_col in df_result.columns and pd.notna(df_result.loc[idx, gl_col]):
                groups_count += 1
                group_num += 1
            else:
                break
        
        total_groups += groups_count
        print(f"第{idx+1}行: 补充了{groups_count}组数据")
    
    print(f"\n总计补充了{total_groups}组GL/Z/Z_LV数据")
    
    # 检查数据完整性
    print("\n=== 数据完整性检查 ===")
    for idx in range(len(df_result)):
        row_name = df_result.iloc[idx, 0]
        group_num = 1
        
        while True:
            gl_col = f'GL{group_num}'
            z_col = f'Z{group_num}'
            z_lv_col = f'Z{group_num}_LV'
            
            if gl_col in df_result.columns and pd.notna(df_result.loc[idx, gl_col]):
                gl_val = df_result.loc[idx, gl_col]
                z_val = df_result.loc[idx, z_col] if z_col in df_result.columns else None
                z_lv_val = df_result.loc[idx, z_lv_col] if z_lv_col in df_result.columns else None
                
                # 检查数据完整性
                issues = []
                if pd.isna(z_val):
                    issues.append("Z值缺失")
                if pd.isna(z_lv_val):
                    issues.append("Z_LV值缺失")
                
                if issues:
                    print(f"第{idx+1}行第{group_num}组数据问题: {', '.join(issues)}")
                
                group_num += 1
            else:
                break

if __name__ == "__main__":
    verify_final_result()
