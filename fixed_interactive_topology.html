<!DOCTYPE html>
<html>
<head>
    <title>苍南环32网络拓扑图 - 交互式版本</title>
    <meta charset="UTF-8">
    <script src="https://unpkg.com/mermaid@10/dist/mermaid.min.js"></script>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #f5f5f5;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .info { 
            background: #e3f2fd; 
            padding: 15px; 
            border-radius: 5px; 
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        #diagram { 
            border: 1px solid #ddd; 
            padding: 20px; 
            border-radius: 5px; 
            background-color: white;
            min-height: 600px;
            text-align: center;
        }
        .legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 20px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 3px;
            border: 2px solid #333;
        }
        .access { background-color: #e1f5fe; }
        .aggregation { background-color: #e8f5e8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>苍南环32网络拓扑图</h1>
        <div class="info">
            <h3>交互功能说明：</h3>
            <ul>
                <li><strong>缩放</strong>：使用鼠标滚轮放大缩小</li>
                <li><strong>拖拽</strong>：点击并拖拽移动视图</li>
                <li><strong>蓝色节点</strong>：本地接入设备 (10个)</li>
                <li><strong>绿色节点</strong>：本地汇聚设备 (2个)</li>
            </ul>
        </div>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color access"></div>
                <span>本地接入设备</span>
            </div>
            <div class="legend-item">
                <div class="legend-color aggregation"></div>
                <span>本地汇聚设备</span>
            </div>
        </div>
        
        <div id="diagram">
            <div class="mermaid">
graph TD
    classDef access fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef aggregation fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px

    N29148["仙堂隧道南口<br/>PTN970C<br/>本地接入"]
    N29139["鹅峰山隧道南口<br/>PTN970C<br/>本地接入"]
    N29147["望坡岭隧道南<br/>PTN970C<br/>本地接入"]
    N29023["大潮村西<br/>970<br/>本地接入"]
    N29004["观美汇聚<br/>7900E-24CRAN<br/>本地汇聚"]
    N29029["分水关高速<br/>970<br/>本地接入"]
    N29005["华阳搬迁<br/>7900E-24<br/>本地汇聚"]
    N29027["石北山一<br/>970<br/>本地接入"]
    N29026["石北山二<br/>970<br/>本地接入"]
    N29025["大潮村<br/>970<br/>本地接入"]
    N29024["官南村<br/>970<br/>本地接入"]
    N29022["山头<br/>970<br/>本地接入"]

    class N29148,N29139,N29147,N29023,N29029,N29027,N29026,N29025,N29024,N29022 access
    class N29004,N29005 aggregation

    N29148 --- N29139
    N29148 --- N29147
    N29147 --- N29023
    N29139 --- N29004
    N29029 --- N29005
    N29029 --- N29027
    N29027 --- N29026
    N29026 --- N29025
    N29025 --- N29024
    N29024 --- N29023
    N29023 --- N29022
    N29022 --- N29004
            </div>
        </div>
        
        <div class="info" style="margin-top: 20px;">
            <h3>网络结构说明：</h3>
            <ul>
                <li><strong>核心汇聚节点</strong>：观美汇聚(7900E-24CRAN)和华阳搬迁(7900E-24)</li>
                <li><strong>温福高铁链路</strong>：仙堂隧道 ↔ 鹅峰山隧道 ↔ 观美汇聚</li>
                <li><strong>桥墩设备链路</strong>：分水关 ↔ 华阳搬迁，石北山一 ↔ 石北山二 ↔ 大潮村 ↔ 官南村</li>
                <li><strong>总连接数</strong>：21条光路连接</li>
            </ul>
        </div>
    </div>
    
    <script>
        // 初始化Mermaid
        mermaid.initialize({ 
            startOnLoad: true, 
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
        
        // 确保图表正确渲染
        document.addEventListener('DOMContentLoaded', function() {
            mermaid.init();
        });
    </script>
</body>
</html>
