import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
from matplotlib.patches import FancyBboxPatch
import networkx as nx

def create_network_topology():
    """根据cnh文件创建网络拓扑图"""

    print("读取文件: cnh_20250805_162949.xlsx")

    # 读取数据
    df = pd.read_excel('cnh_20250805_162949.xlsx')

    # 收集所有网元和连接关系
    nodes = {}  # {设备名: 设备级别}
    connections = []  # [(A端, Z端)]

    for idx, row in df.iterrows():
        a_device = row['A1']
        a_level = row['A1_LV']  # 修正列名

        # 添加A端设备
        if pd.notna(a_device):
            nodes[a_device] = a_level

        # 查找所有Z端设备
        for z_num in [1, 2, 3]:  # Z1, Z2, Z3
            z_col = f'Z{z_num}'
            z_lv_col = f'Z{z_num}_LV'

            if z_col in df.columns and z_lv_col in df.columns:
                z_device = row[z_col]
                z_level = row[z_lv_col]

                if pd.notna(z_device) and pd.notna(z_level):
                    nodes[z_device] = z_level
                    connections.append((a_device, z_device))

    print(f"发现 {len(nodes)} 个网元，{len(connections)} 个连接")
    
    # 创建网络图
    G = nx.Graph()
    
    # 添加节点
    for device, level in nodes.items():
        G.add_node(device, level=level)
    
    # 添加边
    for a, z in connections:
        if a in nodes and z in nodes:
            G.add_edge(a, z)
    
    # 使用spring布局算法自动排列节点
    pos = nx.spring_layout(G, k=3, iterations=50, seed=42)
    
    # 创建图形
    plt.figure(figsize=(20, 15))
    ax = plt.gca()
    
    # 绘制连接线
    for edge in G.edges():
        x1, y1 = pos[edge[0]]
        x2, y2 = pos[edge[1]]
        plt.plot([x1, x2], [y1, y2], 'gray', linewidth=1, alpha=0.7)
    
    # 绘制节点
    for device, (x, y) in pos.items():
        level = nodes[device]
        
        # 根据设备级别选择颜色和形状
        if level == '本地接入':
            color = 'lightblue'
            shape = 'rectangle'
        elif level == '本地汇聚':
            color = 'lightgreen'
            shape = 'rectangle'
        else:
            color = 'lightgray'
            shape = 'rectangle'
        
        # 绘制设备框
        width = 0.15
        height = 0.08
        
        if shape == 'rectangle':
            rect = FancyBboxPatch(
                (x - width/2, y - height/2), width, height,
                boxstyle="round,pad=0.01",
                facecolor=color,
                edgecolor='black',
                linewidth=1
            )
            ax.add_patch(rect)
        
        # 添加设备名称（简化显示）
        device_short = device.split('-')[-1] if '-' in device else device
        if len(device_short) > 15:
            device_short = device_short[:12] + '...'
        
        plt.text(x, y, device_short, 
                ha='center', va='center', 
                fontsize=6, weight='bold',
                wrap=True)
    
    # 添加图例
    legend_elements = [
        patches.Rectangle((0, 0), 1, 1, facecolor='lightblue', edgecolor='black', label='本地接入'),
        patches.Rectangle((0, 0), 1, 1, facecolor='lightgreen', edgecolor='black', label='本地汇聚'),
        patches.Rectangle((0, 0), 1, 1, facecolor='lightgray', edgecolor='black', label='其他')
    ]
    plt.legend(handles=legend_elements, loc='upper right')
    
    plt.title('苍南环32网络拓扑图', fontsize=16, weight='bold')
    plt.axis('off')
    plt.tight_layout()
    
    # 保存图片
    output_file = 'network_topology.png'
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"网络拓扑图已保存为: {output_file}")
    
    plt.show()
    
    # 打印统计信息
    print(f"\n=== 网络统计信息 ===")
    level_count = {}
    for device, level in nodes.items():
        level_count[level] = level_count.get(level, 0) + 1
    
    for level, count in level_count.items():
        print(f"{level}: {count}个设备")
    
    print(f"总连接数: {len(connections)}")
    
    return G, nodes, connections

if __name__ == "__main__":
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    create_network_topology()
