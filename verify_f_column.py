import pandas as pd

def verify_f_column_result():
    """验证F列开始的处理结果"""
    
    # 读取原始文件和结果文件
    df_original = pd.read_excel('苍南环32.xlsx')
    df_result = pd.read_excel('苍南环32_F列补充完成_new.xlsx')
    
    print("=== 验证F列补充结果 ===")
    print(f"原始文件行数: {len(df_original)}")
    print(f"结果文件行数: {len(df_result)}")
    print(f"原始文件列数: {len(df_original.columns)}")
    print(f"结果文件列数: {len(df_result.columns)}")
    
    print("\n=== F列以后的数据补充情况 ===")
    
    # 检查F列以后的数据（从第5列开始，即F列）
    for idx in range(len(df_result)):
        row_name = df_result.iloc[idx, 0]  # A列的设备名称
        print(f"\n第{idx+1}行 ({row_name}):")
        
        # 检查从F列开始的数据
        col_start = 5  # F列
        group_num = 1
        
        for col in range(col_start, len(df_result.columns), 3):
            if col + 2 < len(df_result.columns):
                gl_val = df_result.iloc[idx, col]
                z_val = df_result.iloc[idx, col + 1]
                z_lv_val = df_result.iloc[idx, col + 2]
                
                if pd.notna(gl_val) or pd.notna(z_val) or pd.notna(z_lv_val):
                    print(f"  第{group_num}组: GL{group_num}={gl_val}")
                    print(f"           Z{group_num}={z_val}")
                    print(f"           Z{group_num}_LV={z_lv_val}")
                    group_num += 1
                else:
                    break
    
    print("\n=== 数据统计 ===")
    # 统计每行补充了多少组数据
    for idx in range(len(df_result)):
        row_name = df_result.iloc[idx, 0]
        groups_count = 0
        
        col_start = 5  # F列
        for col in range(col_start, len(df_result.columns), 3):
            if col + 2 < len(df_result.columns):
                gl_val = df_result.iloc[idx, col]
                if pd.notna(gl_val):
                    groups_count += 1
                else:
                    break
        
        print(f"第{idx+1}行: 从F列开始补充了{groups_count}组数据")
    
    print("\n=== 列结构对比 ===")
    print("原始文件列结构:")
    for i, col in enumerate(df_original.columns):
        print(f"  {chr(65+i)}列: {col}")
    
    print("\n结果文件列结构:")
    for i, col in enumerate(df_result.columns):
        print(f"  {chr(65+i)}列: {col}")

if __name__ == "__main__":
    verify_f_column_result()
