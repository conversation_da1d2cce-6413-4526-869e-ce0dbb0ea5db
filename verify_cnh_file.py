import pandas as pd
import glob

def verify_cnh_file():
    """验证生成的cnh文件"""
    
    # 查找最新的cnh文件
    cnh_files = glob.glob('cnh_*.xlsx')
    if not cnh_files:
        print("未找到cnh文件")
        return
    
    latest_file = max(cnh_files)
    print(f"验证文件: {latest_file}")
    
    try:
        df = pd.read_excel(latest_file)
        
        print("\n=== 文件基本信息 ===")
        print(f"行数: {len(df)}")
        print(f"列数: {len(df.columns)}")
        
        print("\n=== 完整列结构 ===")
        for i, col in enumerate(df.columns):
            print(f"  {chr(65+i)}列: {col}")
        
        print("\n=== 补充数据详情 ===")
        for idx in range(len(df)):
            row_name = df.iloc[idx, 0]  # A列的设备名称
            print(f"\n第{idx+1}行 ({row_name}):")
            
            # 查找GL、Z、Z_LV列
            group_num = 1
            found_data = False
            
            while True:
                gl_col = f'GL{group_num}'
                z_col = f'Z{group_num}'
                z_lv_col = f'Z{group_num}_LV'
                
                if gl_col in df.columns:
                    gl_val = df.loc[idx, gl_col]
                    z_val = df.loc[idx, z_col] if z_col in df.columns else None
                    z_lv_val = df.loc[idx, z_lv_col] if z_lv_col in df.columns else None
                    
                    if pd.notna(gl_val):
                        print(f"  第{group_num}组: GL{group_num}={gl_val}")
                        print(f"           Z{group_num}={z_val}")
                        print(f"           Z{group_num}_LV={z_lv_val}")
                        found_data = True
                        group_num += 1
                    else:
                        break
                else:
                    break
            
            if not found_data:
                print("  未找到补充数据")
        
        print("\n=== 数据统计 ===")
        total_groups = 0
        for idx in range(len(df)):
            groups_count = 0
            group_num = 1
            
            while True:
                gl_col = f'GL{group_num}'
                if gl_col in df.columns and pd.notna(df.loc[idx, gl_col]):
                    groups_count += 1
                    group_num += 1
                else:
                    break
            
            total_groups += groups_count
            print(f"第{idx+1}行: 补充了{groups_count}组数据")
        
        print(f"\n总计补充了{total_groups}组GL/Z/Z_LV数据")
        
        # 显示前3行的完整数据
        print("\n=== 前3行完整数据预览 ===")
        print(df.head(3).to_string())
        
    except Exception as e:
        print(f"验证文件失败: {e}")

if __name__ == "__main__":
    verify_cnh_file()
