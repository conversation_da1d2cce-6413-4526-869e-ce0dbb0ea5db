import pandas as pd

def create_interactive_mermaid():
    """创建带有点击事件的交互式Mermaid网络拓扑图"""
    
    print("读取文件: cnh_20250805_162949.xlsx")
    
    # 读取数据
    df = pd.read_excel('cnh_20250805_162949.xlsx')
    
    # 收集所有网元和连接关系
    nodes = {}  # {设备名: 设备级别}
    connections = []  # [(A端, Z端)]
    node_details = {}  # {设备名: 详细信息}
    
    for idx, row in df.iterrows():
        a_device = row['A1']
        a_level = row['A1_LV']
        
        # 添加A端设备
        if pd.notna(a_device):
            nodes[a_device] = a_level
            node_details[a_device] = {
                'level': a_level,
                'subnet': row['关联传输子网'],
                'city': row['地市名称'],
                'county': row['区/县名称']
            }
        
        # 查找所有Z端设备
        for z_num in [1, 2, 3]:  # Z1, Z2, Z3
            z_col = f'Z{z_num}'
            z_lv_col = f'Z{z_num}_LV'
            
            if z_col in df.columns and z_lv_col in df.columns:
                z_device = row[z_col]
                z_level = row[z_lv_col]
                
                if pd.notna(z_device) and pd.notna(z_level):
                    nodes[z_device] = z_level
                    connections.append((a_device, z_device))
                    
                    # 添加Z端设备详细信息（如果还没有）
                    if z_device not in node_details:
                        node_details[z_device] = {
                            'level': z_level,
                            'subnet': 'Unknown',
                            'city': 'Unknown',
                            'county': 'Unknown'
                        }
    
    print(f"发现 {len(nodes)} 个网元，{len(connections)} 个连接")
    
    # 创建节点ID映射（简化名称）
    node_ids = {}
    for i, device in enumerate(nodes.keys()):
        # 提取设备的关键标识
        if '-' in device:
            parts = device.split('-')
            if len(parts) >= 3:
                node_id = f"N{parts[1]}"  # 使用第二部分作为ID
            else:
                node_id = f"N{i+1}"
        else:
            node_id = f"N{i+1}"
        node_ids[device] = node_id
    
    # 生成带有点击事件的Mermaid图表代码
    mermaid_code = "graph TD\n"
    
    # 定义样式类
    mermaid_code += "    classDef access fill:#e1f5fe,stroke:#01579b,stroke-width:2px\n"
    mermaid_code += "    classDef aggregation fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px\n"
    mermaid_code += "    classDef highlight fill:#fff3e0,stroke:#f57c00,stroke-width:3px\n\n"
    
    # 添加节点定义
    access_nodes = []
    aggregation_nodes = []
    
    for device, level in nodes.items():
        node_id = node_ids[device]
        # 简化设备名称显示
        display_name = device.split('-')[-1] if '-' in device else device
        if len(display_name) > 15:
            display_name = display_name[:12] + "..."
        
        # 获取设备详细信息
        details = node_details[device]
        
        # 根据设备级别设置样式
        if level == '本地接入':
            mermaid_code += f'    {node_id}["{display_name}<br/>本地接入"]\n'
            access_nodes.append(node_id)
        elif level == '本地汇聚':
            mermaid_code += f'    {node_id}["{display_name}<br/>本地汇聚"]\n'
            aggregation_nodes.append(node_id)
        else:
            mermaid_code += f'    {node_id}["{display_name}<br/>{level}"]\n'
        
        # 添加点击事件（显示设备详细信息）
        device_info = f"设备: {device}\\n级别: {level}\\n子网: {details['subnet']}\\n地市: {details['city']}\\n区县: {details['county']}"
        mermaid_code += f'    click {node_id} "javascript:alert(\\"{device_info}\\")"\n'
    
    mermaid_code += "\n"
    
    # 应用样式类
    if access_nodes:
        mermaid_code += f"    class {','.join(access_nodes)} access\n"
    if aggregation_nodes:
        mermaid_code += f"    class {','.join(aggregation_nodes)} aggregation\n"
    
    mermaid_code += "\n"
    
    # 添加连接关系
    added_connections = set()
    for a_device, z_device in connections:
        a_id = node_ids[a_device]
        z_id = node_ids[z_device]
        
        # 避免重复连接
        connection_key = tuple(sorted([a_id, z_id]))
        if connection_key not in added_connections:
            mermaid_code += f'    {a_id} --- {z_id}\n'
            added_connections.add(connection_key)
    
    return mermaid_code, nodes, connections, node_details

def save_interactive_mermaid():
    """保存交互式Mermaid代码到文件"""
    mermaid_code, nodes, connections, node_details = create_interactive_mermaid()
    
    # 保存到文件
    with open('interactive_topology.mmd', 'w', encoding='utf-8') as f:
        f.write(mermaid_code)
    
    print("交互式Mermaid拓扑图代码已保存到: interactive_topology.mmd")
    
    # 创建HTML文件用于本地查看
    html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>苍南环32网络拓扑图 - 交互式版本</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .container {{ max-width: 1200px; margin: 0 auto; }}
        .info {{ background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
        #diagram {{ border: 1px solid #ddd; padding: 20px; border-radius: 5px; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>苍南环32网络拓扑图</h1>
        <div class="info">
            <h3>交互功能说明：</h3>
            <ul>
                <li><strong>点击节点</strong>：查看设备详细信息</li>
                <li><strong>鼠标滚轮</strong>：缩放图表</li>
                <li><strong>拖拽</strong>：移动视图</li>
                <li><strong>蓝色节点</strong>：本地接入设备 ({len([n for n, l in nodes.items() if l == '本地接入'])}个)</li>
                <li><strong>绿色节点</strong>：本地汇聚设备 ({len([n for n, l in nodes.items() if l == '本地汇聚'])}个)</li>
            </ul>
        </div>
        <div id="diagram">
            <div class="mermaid">
{mermaid_code}
            </div>
        </div>
    </div>
    
    <script>
        mermaid.initialize({{ startOnLoad: true, theme: 'default' }});
    </script>
</body>
</html>
"""
    
    with open('interactive_topology.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("交互式HTML文件已保存到: interactive_topology.html")
    print("您可以在浏览器中打开此文件查看完整的交互功能")
    
    return mermaid_code

if __name__ == "__main__":
    mermaid_code = save_interactive_mermaid()
    print(f"\n=== 交互式Mermaid代码 ===")
    print(mermaid_code[:500] + "..." if len(mermaid_code) > 500 else mermaid_code)
