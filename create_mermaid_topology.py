import pandas as pd

def create_mermaid_topology():
    """创建Mermaid格式的网络拓扑图"""
    
    print("读取文件: cnh_20250805_162949.xlsx")
    
    # 读取数据
    df = pd.read_excel('cnh_20250805_162949.xlsx')
    
    # 收集所有网元和连接关系
    nodes = {}  # {设备名: 设备级别}
    connections = []  # [(A端, Z端)]
    
    for idx, row in df.iterrows():
        a_device = row['A1']
        a_level = row['A1_LV']
        
        # 添加A端设备
        if pd.notna(a_device):
            nodes[a_device] = a_level
        
        # 查找所有Z端设备
        for z_num in [1, 2, 3]:  # Z1, Z2, Z3
            z_col = f'Z{z_num}'
            z_lv_col = f'Z{z_num}_LV'
            
            if z_col in df.columns and z_lv_col in df.columns:
                z_device = row[z_col]
                z_level = row[z_lv_col]
                
                if pd.notna(z_device) and pd.notna(z_level):
                    nodes[z_device] = z_level
                    connections.append((a_device, z_device))
    
    print(f"发现 {len(nodes)} 个网元，{len(connections)} 个连接")
    
    # 创建节点ID映射（简化名称）
    node_ids = {}
    for i, device in enumerate(nodes.keys()):
        # 提取设备的关键标识
        if '-' in device:
            parts = device.split('-')
            if len(parts) >= 3:
                node_id = f"N{parts[1]}"  # 使用第二部分作为ID
            else:
                node_id = f"N{i+1}"
        else:
            node_id = f"N{i+1}"
        node_ids[device] = node_id
    
    # 生成Mermaid图表代码
    mermaid_code = "graph TD\n"

    # 定义样式类
    mermaid_code += "    classDef access fill:#e1f5fe,stroke:#01579b,stroke-width:2px\n"
    mermaid_code += "    classDef aggregation fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px\n\n"

    # 添加节点定义
    access_nodes = []
    aggregation_nodes = []

    for device, level in nodes.items():
        node_id = node_ids[device]
        # 简化设备名称显示
        display_name = device.split('-')[-1] if '-' in device else device
        if len(display_name) > 20:
            display_name = display_name[:17] + "..."

        # 根据设备级别设置样式
        if level == '本地接入':
            mermaid_code += f'    {node_id}["{display_name}<br/>本地接入"]\n'
            access_nodes.append(node_id)
        elif level == '本地汇聚':
            mermaid_code += f'    {node_id}["{display_name}<br/>本地汇聚"]\n'
            aggregation_nodes.append(node_id)
        else:
            mermaid_code += f'    {node_id}["{display_name}<br/>{level}"]\n'

    mermaid_code += "\n"

    # 应用样式类
    if access_nodes:
        mermaid_code += f"    class {','.join(access_nodes)} access\n"
    if aggregation_nodes:
        mermaid_code += f"    class {','.join(aggregation_nodes)} aggregation\n"

    mermaid_code += "\n"
    
    # 添加连接关系
    added_connections = set()
    for a_device, z_device in connections:
        a_id = node_ids[a_device]
        z_id = node_ids[z_device]
        
        # 避免重复连接
        connection_key = tuple(sorted([a_id, z_id]))
        if connection_key not in added_connections:
            mermaid_code += f'    {a_id} --- {z_id}\n'
            added_connections.add(connection_key)
    
    return mermaid_code, nodes, connections

def save_mermaid_file():
    """保存Mermaid代码到文件"""
    mermaid_code, nodes, connections = create_mermaid_topology()
    
    # 保存到文件
    with open('network_topology.mmd', 'w', encoding='utf-8') as f:
        f.write(mermaid_code)
    
    print("Mermaid拓扑图代码已保存到: network_topology.mmd")
    
    # 打印统计信息
    print(f"\n=== 网络统计信息 ===")
    level_count = {}
    for device, level in nodes.items():
        level_count[level] = level_count.get(level, 0) + 1
    
    for level, count in level_count.items():
        print(f"{level}: {count}个设备")
    
    print(f"总连接数: {len(connections)}")
    
    # 打印设备列表
    print(f"\n=== 设备列表 ===")
    for device, level in nodes.items():
        print(f"{device} - {level}")
    
    return mermaid_code

if __name__ == "__main__":
    mermaid_code = save_mermaid_file()
    print(f"\n=== Mermaid代码 ===")
    print(mermaid_code)
