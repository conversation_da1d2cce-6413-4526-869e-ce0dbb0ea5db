import pandas as pd
import glob

def check_cnh_columns():
    """检查cnh文件的列结构"""
    
    # 查找最新的cnh文件
    cnh_files = glob.glob('cnh_*.xlsx')
    if not cnh_files:
        print("未找到cnh文件")
        return
    
    latest_file = max(cnh_files)
    print(f"检查文件: {latest_file}")
    
    df = pd.read_excel(latest_file)
    
    print(f"行数: {len(df)}")
    print(f"列数: {len(df.columns)}")
    
    print("\n所有列名:")
    for i, col in enumerate(df.columns):
        print(f"  {i}: {col}")
    
    print("\n前2行数据:")
    print(df.head(2))

if __name__ == "__main__":
    check_cnh_columns()
